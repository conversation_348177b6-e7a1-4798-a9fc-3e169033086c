#!/usr/bin/env node
// Node 22 LTS — genera /etc/mediamtx/mediamtx.yml da CSV (telecamere + utenti).
// CSV:
//   /etc/camserver/cameras.csv -> nome,ip,user,password
//   /etc/camserver/users.csv   -> user,password
// Le righe che iniziano con "#" o i commenti inline dopo "#" vengono ignorati.

import { readFileSync, writeFileSync, copyFileSync, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

// --- PERCORSI ---------------------------------------------------------------
const CAMS_FILE = '/etc/camserver/cameras.csv';
const USERS_FILE = '/etc/camserver/users.csv';
const MTX_CFG    = '/etc/mediamtx/mediamtx.yml';

// Registrazioni: /mnt/Dati/registrazioni/<NomeCam>/<NomeCam>_%Y-%m-%d_%H-%M-%S
const RECORD_ROOT = '/mnt/Dati/registrazioni';
const BACKUP      = `/etc/mediamtx/mediamtx.yml.bak-${Date.now()}`;

// --- TEMPLATE RTSP (come richiesto) -----------------------------------------
const RTSP_TEMPLATE = 'rtsp://{user}:{pass}@{ip}:554/profile1';

// --- UTILS ------------------------------------------------------------------
function stripInlineComment(line) {
  const ix = line.indexOf('#');
  return (ix >= 0 ? line.slice(0, ix) : line).trim();
}

function parseCsv(path, cols) {
  const raw = readFileSync(path, 'utf8');
  return raw
    .split(/\r?\n/)
    .map(stripInlineComment)
    .filter(l => l.length > 0)
    .map(l => l.split(',').map(s => s.trim()))
    .map(arr => {
      if (arr.length < cols || arr.some(s => s.length === 0)) {
        throw new Error(`Riga non valida in ${path}: "${arr.join(',')}"`);
      }
      return arr;
    });
}

// YAML escape minimale per scalari
function yEsc(str) {
  if (/^[A-Za-z0-9._:@\/\-\[\]\{\}%]+$/.test(str)) return str;
  return JSON.stringify(str);
}

// --- LETTURA CSV ------------------------------------------------------------
const cams = parseCsv(CAMS_FILE, 4).map(([name, ip, user, pass]) => ({
  name, ip, user, pass,
  rtsp: RTSP_TEMPLATE
    .replace('{ip}', ip)
    .replace('{user}', encodeURIComponent(user))
    .replace('{pass}', encodeURIComponent(pass)),
}));

const users = parseCsv(USERS_FILE, 2).map(([user, pass]) => ({ user, pass }));

if (cams.length === 0) throw new Error('Nessuna telecamera definita in cameras.csv');
if (users.length === 0) console.warn('Attenzione: nessun utente definito in users.csv (nessuno potrà leggere).');

// --- CHECK RECORD_ROOT ------------------------------------------------------
if (!existsSync(RECORD_ROOT)) {
  console.error(`ERRORE: directory di registrazione non presente: ${RECORD_ROOT}\n` +
                `       Monta/crea il percorso e riprova.`);
  process.exit(1);
}

// Crea la cartella per ogni camera, se mancante
for (const c of cams) {
  const camDir = `${RECORD_ROOT}/${c.name}`;
  if (!existsSync(camDir)) {
    mkdirSync(camDir, { recursive: true });
    console.log(`Creata directory: ${camDir}`);
  }
}

// --- COSTRUZIONE mediamtx.yml ----------------------------------------------
let y = '';
y += 'logLevel: info\n';
y += '\n';
y += '# API solo locale (gestione da Node)\n';
y += 'api: yes\n';
y += 'apiAddress: 127.0.0.1:9997\n';
y += '\n';
y += '# RTSP server (solo TCP per i client)\n';
y += 'rtsp: yes\n';
y += 'rtspAddress: :8554\n';
y += 'rtspTransports: [tcp]\n';
y += '\n';
y += '# Autenticazione interna: utenti con permesso read su TUTTI i path\n';
y += 'authMethod: internal\n';
y += 'authInternalUsers:\n';
for (const u of users) {
  y += `  - user: ${yEsc(u.user)}\n`;
  y += `    pass: ${yEsc(u.pass)}\n`;
  y += `    ips: []\n`;
  y += `    permissions:\n`;
  y += `      - action: read\n`;
  y += `        path: \n`; // path vuoto = tutti i path
}
if (users.length === 0) y += '  []\n';

y += '\n';
y += '# Default per TUTTI i path (registrazione fMP4 1h, pull RTSP su TCP)\n';
y += 'pathDefaults:\n';
y += '  record: yes\n';
y += '  recordFormat: fmp4\n';
y += `  recordPath: ${yEsc(RECORD_ROOT)}/%path/%path_%Y-%m-%d_%H-%M-%S\n`;
y += '  recordPartDuration: 1s\n';
y += '  recordSegmentDuration: 1h\n';
y += '  recordDeleteAfter: 0s\n';
y += '  rtspTransport: tcp\n';
y += '\n';
y += '# Path delle telecamere (pull RTSP costante)\n';
y += 'paths:\n';
for (const c of cams) {
  y += `  ${c.name}:\n`;
  y += `    source: ${yEsc(c.rtsp)}\n`;
}
y += '\n';

// --- BACKUP & SCRITTURA -----------------------------------------------------
if (!existsSync(dirname(MTX_CFG))) {
  mkdirSync(dirname(MTX_CFG), { recursive: true });
}
if (existsSync(MTX_CFG)) {
  copyFileSync(MTX_CFG, BACKUP);
  console.log(`Backup creato: ${BACKUP}`);
}
writeFileSync(MTX_CFG, y, 'utf8');
console.log(`Scritto ${MTX_CFG}.`);

console.log('\nTelecamere configurate:');
for (const c of cams) console.log(`- ${c.name} -> ${c.rtsp}`);
console.log('\nUtenti abilitati (read):', users.map(u => u.user).join(', '));
console.log('\nRegistrazione in:', `${RECORD_ROOT}/<NomeCam>/<NomeCam>_%Y-%m-%d_%H-%M-%S`);
console.log('Pronto. MediaMTX rileggerà la config (hot-reload).');
