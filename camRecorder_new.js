#!/usr/bin/env node
'use strict';

// ========================== CONFIGURAZIONE ==========================
const PATHS = {
  RECORD_ROOT: '/mnt/Dati/registrazioni',
  DISK_CHECK:  '/mnt/Dati',
  CAMERAS_CSV: '/etc/camserver/cameras.csv',  // NomeCam,IP,user,password
  USERS_CSV:   '/etc/camserver/users.csv',    // user,password
  MTX_DIR:     '/etc/mediamtx',
  MTX_FILE:    '/etc/mediamtx/mediamtx.yml'
};

const SETTINGS = {
  FREE_MIN_BYTES: 15n * 1024n * 1024n * 1024n, // 15 GiB
  CLEAN_INTERVAL_MS: 10 * 60 * 1000,           // 10 minuti
  UNLINK_CONCURRENCY: 32,
  RECORD_PART: '1s',
  RECORD_SEGMENT: '1h',
  RTSP_PORT: 8554
};

const RTSP_TEMPLATE = 'rtsp://{user}:{pass}@{ip}:554/profile1';

// ============================ IMPORTS ===============================
const fs = require('fs');
const fsp = require('fs/promises');
const path = require('path');

// ============================ UTILS CSV ============================
function stripInlineComment(line) {
  const ix = line.indexOf('#');
  return (ix >= 0 ? line.slice(0, ix) : line).trim();
}
async function parseCsv(filePath, minCols) {
  let raw;
  try { raw = await fsp.readFile(filePath, 'utf8'); }
  catch (e) { throw new Error(`Impossibile leggere ${filePath}: ${e.message}`); }
  const rows = [];
  for (const orig of raw.split(/\r?\n/)) {
    const line = stripInlineComment(orig);
    if (!line) continue;
    const cols = line.split(',').map(s => s.trim());
    if (cols.length < minCols || cols.some(s => s.length === 0)) {
      throw new Error(`Riga non valida in ${filePath}: "${orig}"`);
    }
    rows.push(cols);
  }
  return rows;
}
function yamlEsc(str) {
  return /^[A-Za-z0-9._:@\/\-\[\]\{\}%]+$/.test(str) ? str : JSON.stringify(str);
}
function todayString() {
  const d = new Date();
  return `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')}`;
}
function urlWithCreds(ip, user, pass) {
  return RTSP_TEMPLATE
    .replace('{ip}', ip)
    .replace('{user}', encodeURIComponent(user))
    .replace('{pass}', encodeURIComponent(pass));
}

// ===================== CHECK INIZIALI & FS =====================
async function validateCriticalPaths() {
  const checks = [
    { path: PATHS.DISK_CHECK, name: 'Mount point dati', mustExist: true },
    { path: PATHS.RECORD_ROOT, name: 'Directory registrazioni', mustExist: true, mustWritable: true },
    { path: PATHS.CAMERAS_CSV, name: 'File cameras.csv', mustExist: true },
    { path: PATHS.USERS_CSV, name: 'File users.csv', mustExist: true },
    { path: path.dirname(PATHS.MTX_FILE), name: 'Directory MediaMTX', mustExist: true, mustWritable: true }
  ];

  const errors = [];

  for (const check of checks) {
    try {
      await fsp.access(check.path, fs.constants.F_OK);

      const stat = await fsp.stat(check.path);
      const isDir = stat.isDirectory();
      const isFile = stat.isFile();

      if (check.name.includes('Directory') && !isDir) {
        errors.push(`${check.name}: ${check.path} non è una directory`);
        continue;
      }

      if (check.name.includes('File') && !isFile) {
        errors.push(`${check.name}: ${check.path} non è un file`);
        continue;
      }

      if (check.mustWritable) {
        try {
          await fsp.access(check.path, fs.constants.W_OK);
        } catch {
          errors.push(`${check.name}: ${check.path} non è scrivibile`);
        }
      }

      console.log(`[init] ✓ ${check.name}: ${check.path}`);

    } catch (e) {
      if (check.mustExist) {
        errors.push(`${check.name}: ${check.path} non accessibile (${e.code || e.message})`);
      } else {
        console.warn(`[init] ⚠ ${check.name}: ${check.path} non trovato (opzionale)`);
      }
    }
  }

  if (errors.length > 0) {
    throw new Error(`Validazione percorsi fallita:\n${errors.map(e => `  - ${e}`).join('\n')}`);
  }

  console.log('[init] Tutti i percorsi critici sono validi');
}

async function ensureDirExists(dir, mustWritable = false) {
  let st;
  try { st = await fsp.stat(dir); } catch { throw new Error(`Directory mancante: ${dir}`); }
  if (!st.isDirectory()) throw new Error(`Percorso non è una directory: ${dir}`);
  if (mustWritable) {
    try { await fsp.access(dir, fs.constants.W_OK); }
    catch { throw new Error(`Directory non scrivibile: ${dir}`); }
  }
}

async function ensureFileParentWritable(filePath) {
  await ensureDirExists(path.dirname(filePath), true);
}

// ======================= GENERAZIONE YAML ===========================
async function generateMediamtxYaml(cameras, users) {
  // Crea dir camera se manca
  for (const c of cameras) {
    const camDir = path.join(PATHS.RECORD_ROOT, c.name);
    await fsp.mkdir(camDir, { recursive: true });
  }

  let y = '';
  y += 'logLevel: info\n\n';
  y += 'api: yes\n';
  y += 'apiAddress: 127.0.0.1:9997\n\n';
  y += 'rtsp: yes\n';
  y += `rtspAddress: :${SETTINGS.RTSP_PORT}\n`;
  y += 'rtspTransports: [tcp]\n\n';
  y += 'authMethod: internal\n';
  y += 'authInternalUsers:\n';
  if (users.length === 0) { y += '  []\n\n'; }
  else {
    for (const u of users) {
      y += `  - user: ${yamlEsc(u.user)}\n`;
      y += `    pass: ${yamlEsc(u.pass)}\n`;
      y += '    ips: []\n';
      y += '    permissions:\n';
      y += '      - action: read\n';
      y += '        path: ""\n';
    }
    y += '\n';
  }
  y += 'pathDefaults:\n';
  y += '  record: yes\n';
  y += '  recordFormat: fmp4\n';
  y += `  recordPath: ${yamlEsc(PATHS.RECORD_ROOT)}/%path/%path_%Y-%m-%d_%H-%M-%S\n`;
  y += `  recordPartDuration: ${SETTINGS.RECORD_PART}\n`;
  y += `  recordSegmentDuration: ${SETTINGS.RECORD_SEGMENT}\n`;
  y += '  recordDeleteAfter: 0s\n';
  y += '  rtspTransport: tcp\n\n';
  y += 'paths:\n';
  for (const c of cameras) {
    y += `  ${c.name}:\n`;
    y += `    source: ${yamlEsc(c.rtsp)}\n`;
  }
  y += '\n';

  await ensureFileParentWritable(PATHS.MTX_FILE);
  try {
    await fsp.access(PATHS.MTX_FILE, fs.constants.F_OK);
    const backup = `${PATHS.MTX_FILE}.bak-${Date.now()}`;
    await fsp.copyFile(PATHS.MTX_FILE, backup);
    console.log(`[config] Backup creato: ${backup}`);
  } catch {}
  await fsp.writeFile(PATHS.MTX_FILE, y, 'utf8');
  console.log(`[config] Scritto ${PATHS.MTX_FILE}`);
}

// ============================ REGEN WRAPPER =========================
let regenLock = false;
async function regenerateFromCsv(reason = 'manual') {
  if (regenLock) {
    console.log(`[config] Rigenerazione già in corso, skip (${reason})`);
    return;
  }
  regenLock = true;
  try {
    const camRows = await parseCsv(PATHS.CAMERAS_CSV, 4); // NomeCam,IP,user,password
    const userRows = await parseCsv(PATHS.USERS_CSV, 2);  // user,password

    const cameras = camRows.map(([name, ip, user, pass]) => ({
      name, ip, user, pass, rtsp: urlWithCreds(ip, user, pass)
    }));
    if (cameras.length === 0) throw new Error('Nessuna telecamera in cameras.csv');

    const users = userRows.map(([user, pass]) => ({ user, pass }));
    if (users.length === 0) console.warn('[config] Nessun utente in users.csv: nessun accesso RTSP.');

    await generateMediamtxYaml(cameras, users);
    console.log(`[config] Rigenerazione completata (${reason}).`);
  } catch (e) {
    console.error(`[config] ERRORE rigenerazione (${reason}):`, e.message || e);
  } finally {
    regenLock = false;
  }
}

// ============================ JANITOR ===============================
let janitorRunning = false;
let janitorInterval = null;

async function getFreeBytes(mountPath, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // Verifica che il path esista prima di chiamare statfs
      await fsp.access(mountPath, fs.constants.F_OK);

      const s = await fsp.statfs(mountPath);

      // Validazione dei valori restituiti
      if (!s || typeof s.bavail !== 'number' || typeof s.bsize !== 'number') {
        throw new Error('Dati statfs non validi');
      }

      if (s.bavail < 0 || s.bsize <= 0) {
        throw new Error(`Valori statfs non validi: bavail=${s.bavail}, bsize=${s.bsize}`);
      }

      const freeBytes = BigInt(s.bavail) * BigInt(s.bsize);
      console.log(`[janitor] Spazio libero su ${mountPath}: ${s.bavail} blocchi × ${s.bsize} bytes = ${freeBytes} bytes`);

      return freeBytes;

    } catch (e) {
      const isLastAttempt = attempt === retries;

      if (e.code === 'ENOENT') {
        throw new Error(`Mount point non trovato: ${mountPath}`);
      } else if (e.code === 'EACCES') {
        throw new Error(`Accesso negato al mount point: ${mountPath}`);
      } else if (e.code === 'ENOTDIR') {
        throw new Error(`Il percorso non è una directory: ${mountPath}`);
      } else if (e.code === 'EIO') {
        if (isLastAttempt) {
          throw new Error(`Errore I/O persistente su ${mountPath} dopo ${retries} tentativi`);
        }
        console.warn(`[janitor] Errore I/O su ${mountPath}, tentativo ${attempt}/${retries}: ${e.message}`);
        // Attesa progressiva: 1s, 2s, 3s
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      } else {
        if (isLastAttempt) {
          throw new Error(`Impossibile ottenere spazio libero per ${mountPath} dopo ${retries} tentativi: ${e.message}`);
        }
        console.warn(`[janitor] Errore generico su ${mountPath}, tentativo ${attempt}/${retries}: ${e.message}`);
        await new Promise(resolve => setTimeout(resolve, attempt * 500));
        continue;
      }
    }
  }
}
async function getFreeBytesFallback(mountPath) {
  console.warn(`[janitor] Usando fallback per ${mountPath}`);
  try {
    // Fallback: usa 'df' command se disponibile
    const { spawn } = require('child_process');
    return new Promise((resolve, reject) => {
      const df = spawn('df', ['-B1', mountPath], { stdio: ['ignore', 'pipe', 'pipe'] });
      let output = '';
      let error = '';

      df.stdout.on('data', (data) => { output += data.toString(); });
      df.stderr.on('data', (data) => { error += data.toString(); });

      df.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`df command failed: ${error}`));
          return;
        }

        const lines = output.trim().split('\n');
        if (lines.length < 2) {
          reject(new Error('df output non valido'));
          return;
        }

        const fields = lines[1].trim().split(/\s+/);
        if (fields.length < 4) {
          reject(new Error('df fields non validi'));
          return;
        }

        const available = parseInt(fields[3], 10);
        if (isNaN(available) || available < 0) {
          reject(new Error(`df available non valido: ${fields[3]}`));
          return;
        }

        console.log(`[janitor] Fallback df: ${available} bytes disponibili`);
        resolve(BigInt(available));
      });

      // Timeout per il comando df
      setTimeout(() => {
        df.kill('SIGTERM');
        reject(new Error('df command timeout'));
      }, 10000);
    });
  } catch (e) {
    throw new Error(`Fallback fallito: ${e.message}`);
  }
}

async function listCameraDirs() {
  try {
    const entries = await fsp.readdir(PATHS.RECORD_ROOT, { withFileTypes: true });
    return entries.filter(d => d.isDirectory()).map(d => d.name);
  } catch (e) {
    if (e.code === 'ENOENT') {
      throw new Error(`Directory registrazioni non trovata: ${PATHS.RECORD_ROOT}`);
    } else if (e.code === 'EACCES') {
      throw new Error(`Accesso negato alla directory: ${PATHS.RECORD_ROOT}`);
    } else {
      throw new Error(`Errore lettura directory ${PATHS.RECORD_ROOT}: ${e.message}`);
    }
  }
}
async function scanAllFilesOnce(camDirs) {
  const today = todayString();
  const perDate = new Map();
  for (const cam of camDirs) {
    const camDir = path.join(PATHS.RECORD_ROOT, cam);
    let ents; try { ents = await fsp.readdir(camDir, { withFileTypes: true }); }
    catch (e) { console.error(`[janitor] Errore dir ${camDir}: ${e.message}`); continue; }
    const camEsc = cam.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const re = new RegExp(`^${camEsc}_(\\d{4}-\\d{2}-\\d{2})_\\d{2}-\\d{2}-\\d{2}\\.[A-Za-z0-9]+$`);
    for (const de of ents) {
      if (!de.isFile()) continue;
      const m = de.name.match(re);
      if (!m) continue;
      const dateStr = m[1];
      if (dateStr === today) continue;
      const full = path.join(camDir, de.name);
      if (!perDate.has(dateStr)) perDate.set(dateStr, []);
      perDate.get(dateStr).push(full);
    }
  }
  const daysSorted = Array.from(perDate.keys()).sort();
  return { perDate, daysSorted };
}
async function deleteFilesBatch(filePaths) {
  const N = SETTINGS.UNLINK_CONCURRENCY;
  let deleted = 0, failed = 0;
  for (let i = 0; i < filePaths.length; i += N) {
    const chunk = filePaths.slice(i, i + N);
    await Promise.all(chunk.map(async p => {
      try { await fsp.unlink(p); deleted++; }
      catch (e) {
        if (e && (e.code === 'ENOENT' || e.code === 'ESTALE')) return;
        failed++; console.error('[janitor] Errore unlink', p, e.message);
      }
    }));
  }
  return { deleted, failed };
}
async function janitorCycle() {
  // Controllo con timeout per evitare lock permanenti
  if (janitorRunning) {
    console.log('[janitor] Ciclo già in esecuzione, skip');
    return;
  }

  janitorRunning = true;
  const startTime = Date.now();

  // Timeout di sicurezza per evitare lock permanenti (30 minuti)
  const safetyTimeout = setTimeout(() => {
    console.error('[janitor] TIMEOUT: reset forzato del lock dopo 30 minuti');
    janitorRunning = false;
  }, 30 * 60 * 1000);

  try {
    await ensureDirExists(PATHS.RECORD_ROOT);

    // Tentativo di ottenere spazio libero con fallback
    let free;
    try {
      free = await getFreeBytes(PATHS.DISK_CHECK);
    } catch (primaryError) {
      console.warn(`[janitor] Errore primario getFreeBytes: ${primaryError.message}`);
      try {
        free = await getFreeBytesFallback(PATHS.DISK_CHECK);
        console.log('[janitor] Fallback riuscito');
      } catch (fallbackError) {
        console.error(`[janitor] Anche il fallback è fallito: ${fallbackError.message}`);
        // In caso di errore totale, assumiamo spazio insufficiente per sicurezza
        console.warn('[janitor] Assumendo spazio insufficiente per sicurezza');
        free = 0n;
      }
    }

    // Conversione più sicura per evitare overflow
    const freeGiB = free > Number.MAX_SAFE_INTEGER
      ? Number.MAX_SAFE_INTEGER
      : Number(free / (1024n * 1024n * 1024n));

    console.log(`[janitor] Free: ${freeGiB} GiB (soglia 15 GiB)`);
    if (free >= SETTINGS.FREE_MIN_BYTES) {
      console.log('[janitor] Spazio sufficiente, nessuna pulizia necessaria');
      return;
    }

    const camDirs = await listCameraDirs();
    if (camDirs.length === 0) {
      console.warn('[janitor] Nessuna cartella telecamera.');
      return;
    }

    const { perDate, daysSorted } = await scanAllFilesOnce(camDirs);
    if (daysSorted.length === 0) {
      console.warn('[janitor] Nessun giorno storico da eliminare.');
      return;
    }

    const oldestDay = daysSorted[0];
    const targets = perDate.get(oldestDay) || [];
    if (targets.length === 0) {
      console.warn(`[janitor] Nessun file da eliminare per ${oldestDay}.`);
      return;
    }

    console.log(`[janitor] Sotto soglia: elimino ${oldestDay} (${targets.length} files).`);
    const { deleted, failed } = await deleteFilesBatch(targets);
    const elapsed = Date.now() - startTime;
    console.log(`[janitor] ${oldestDay}: eliminati ${deleted}, falliti ${failed}, tempo ${elapsed} ms.`);

  } catch (e) {
    console.error('[janitor] Errore generale:', e.message || e);
    // In caso di errore, logga dettagli per debug
    console.error('[janitor] Stack trace:', e.stack);
  } finally {
    clearTimeout(safetyTimeout);
    janitorRunning = false;
    const totalTime = Date.now() - startTime;
    console.log(`[janitor] Ciclo completato in ${totalTime} ms`);
  }
}

// ============================ WATCH CAMERAS.CSV =====================
let watchTimer = null;
let isWatcherActive = false;

function installCamerasWatcher() {
  // Evita watcher multipli
  if (isWatcherActive) {
    console.log('[config] Watcher già attivo, skip');
    return;
  }

  // fs.watchFile è più robusto ai salvataggi “atomic write” degli editor
  try {
    fs.watchFile(PATHS.CAMERAS_CSV, { persistent: true, interval: 1000 }, (curr, prev) => {
      if (curr.mtimeMs === prev.mtimeMs) return;
      if (watchTimer) clearTimeout(watchTimer);
      // debounce 500ms per evitare parse durante scrittura parziale
      watchTimer = setTimeout(() => regenerateFromCsv('watch:cameras.csv'), 500);
    });
    isWatcherActive = true;
    console.log('[config] Watch attivo su', PATHS.CAMERAS_CSV);
  } catch (e) {
    console.error('[config] Impossibile impostare il watcher:', e.message);
  }
}

function startJanitorInterval() {
  if (janitorInterval) {
    console.log('[janitor] Interval già attivo');
    return;
  }
  janitorInterval = setInterval(janitorCycle, SETTINGS.CLEAN_INTERVAL_MS);
  console.log(`[janitor] Interval avviato ogni ${SETTINGS.CLEAN_INTERVAL_MS/1000} secondi`);
}

function stopJanitorInterval() {
  if (janitorInterval) {
    clearInterval(janitorInterval);
    janitorInterval = null;
    console.log('[janitor] Interval fermato');
  }
}

function cleanupWatcher() {
  if (isWatcherActive) {
    try {
      fs.unwatchFile(PATHS.CAMERAS_CSV);
      if (watchTimer) {
        clearTimeout(watchTimer);
        watchTimer = null;
      }
      isWatcherActive = false;
      console.log('[config] Watcher rimosso');
    } catch (e) {
      console.error('[config] Errore rimozione watcher:', e.message);
    }
  }
}

function cleanupAll() {
  console.log('[camserver] Cleanup generale in corso...');
  cleanupWatcher();
  stopJanitorInterval();

  // Reset forzato del janitor se bloccato
  if (janitorRunning) {
    console.log('[janitor] Reset forzato del lock');
    janitorRunning = false;
  }
}

// Gestione graceful shutdown
function setupGracefulShutdown() {
  const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
  signals.forEach(signal => {
    process.on(signal, () => {
      console.log(`[camserver] Ricevuto ${signal}, shutdown in corso...`);
      cleanupAll();
      process.exit(0);
    });
  });

  // Gestione errori non catturati
  process.on('uncaughtException', (err) => {
    console.error('[camserver] Errore non gestito:', err);
    console.error('[camserver] Stack:', err.stack);
    cleanupAll();
    process.exit(1);
  });

  process.on('unhandledRejection', (reason) => {
    console.error('[camserver] Promise rejection non gestita:', reason);
    cleanupAll();
    process.exit(1);
  });
}

// ============================ BOOTSTRAP =============================
(async () => {
  try {
    console.log('[camserver] Avvio in corso...');

    // Setup gestione graceful shutdown
    setupGracefulShutdown();

    // Validazione completa dei percorsi critici
    console.log('[init] Validazione percorsi critici...');
    await validateCriticalPaths();

    // Check aggiuntivi per compatibilità
    await ensureDirExists(PATHS.MTX_DIR, true);
    await ensureFileParentWritable(PATHS.MTX_FILE);
    await ensureDirExists(PATHS.RECORD_ROOT, true);

    // Prima generazione
    console.log('[config] Generazione configurazione iniziale...');
    await regenerateFromCsv('startup');

    // Avvia watcher su cameras.csv
    console.log('[config] Avvio watcher configurazione...');
    installCamerasWatcher();

    // Primo ciclo janitor e avvio interval
    console.log('[janitor] Avvio primo ciclo di pulizia...');
    await janitorCycle();
    startJanitorInterval();

    console.log('[camserver] ✓ Avviato con successo: config generata, watcher e janitor attivi.');
  } catch (e) {
    console.error('[camserver] ✗ ERRORE AVVIO:', e.message || e);
    if (e.stack) {
      console.error('[camserver] Stack trace:', e.stack);
    }
    cleanupAll(); // Cleanup completo anche in caso di errore di avvio
    process.exit(1);
  }
})();
