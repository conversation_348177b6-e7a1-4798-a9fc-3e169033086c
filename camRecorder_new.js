#!/usr/bin/env node
'use strict';

// ========================== CONFIGURAZIONE ==========================
const PATHS = {
  RECORD_ROOT: '/mnt/Dati/registrazioni',
  DISK_CHECK:  '/mnt/Dati',
  CAMERAS_CSV: '/etc/camserver/cameras.csv',  // NomeCam,IP,user,password
  USERS_CSV:   '/etc/camserver/users.csv',    // user,password
  MTX_DIR:     '/etc/mediamtx',
  MTX_FILE:    '/etc/mediamtx/mediamtx.yml'
};

const SETTINGS = {
  FREE_MIN_BYTES: 15n * 1024n * 1024n * 1024n, // 15 GiB
  CLEAN_INTERVAL_MS: 10 * 60 * 1000,           // 10 minuti
  UNLINK_CONCURRENCY: 32,
  RECORD_PART: '1s',
  RECORD_SEGMENT: '1h',
  RTSP_PORT: 8554
};

const RTSP_TEMPLATE = 'rtsp://{user}:{pass}@{ip}:554/profile1';

// ============================ IMPORTS ===============================
const fs = require('fs');
const fsp = require('fs/promises');
const path = require('path');

// ============================ UTILS CSV ============================
function stripInlineComment(line) {
  const ix = line.indexOf('#');
  return (ix >= 0 ? line.slice(0, ix) : line).trim();
}
async function parseCsv(filePath, minCols) {
  let raw;
  try { raw = await fsp.readFile(filePath, 'utf8'); }
  catch (e) { throw new Error(`Impossibile leggere ${filePath}: ${e.message}`); }
  const rows = [];
  for (const orig of raw.split(/\r?\n/)) {
    const line = stripInlineComment(orig);
    if (!line) continue;
    const cols = line.split(',').map(s => s.trim());
    if (cols.length < minCols || cols.some(s => s.length === 0)) {
      throw new Error(`Riga non valida in ${filePath}: "${orig}"`);
    }
    rows.push(cols);
  }
  return rows;
}
function yamlEsc(str) {
  return /^[A-Za-z0-9._:@\/\-\[\]\{\}%]+$/.test(str) ? str : JSON.stringify(str);
}
function todayString() {
  const d = new Date();
  return `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')}`;
}
function urlWithCreds(ip, user, pass) {
  return RTSP_TEMPLATE
    .replace('{ip}', ip)
    .replace('{user}', encodeURIComponent(user))
    .replace('{pass}', encodeURIComponent(pass));
}

// ===================== CHECK INIZIALI & FS =====================
async function ensureDirExists(dir, mustWritable = false) {
  let st;
  try { st = await fsp.stat(dir); } catch { throw new Error(`Directory mancante: ${dir}`); }
  if (!st.isDirectory()) throw new Error(`Percorso non è una directory: ${dir}`);
  if (mustWritable) {
    try { await fsp.access(dir, fs.constants.W_OK); }
    catch { throw new Error(`Directory non scrivibile: ${dir}`); }
  }
}
async function ensureFileParentWritable(filePath) {
  await ensureDirExists(path.dirname(filePath), true);
}

// ======================= GENERAZIONE YAML ===========================
async function generateMediamtxYaml(cameras, users) {
  // Crea dir camera se manca
  for (const c of cameras) {
    const camDir = path.join(PATHS.RECORD_ROOT, c.name);
    await fsp.mkdir(camDir, { recursive: true });
  }

  let y = '';
  y += 'logLevel: info\n\n';
  y += 'api: yes\n';
  y += 'apiAddress: 127.0.0.1:9997\n\n';
  y += 'rtsp: yes\n';
  y += `rtspAddress: :${SETTINGS.RTSP_PORT}\n`;
  y += 'rtspTransports: [tcp]\n\n';
  y += 'authMethod: internal\n';
  y += 'authInternalUsers:\n';
  if (users.length === 0) { y += '  []\n\n'; }
  else {
    for (const u of users) {
      y += `  - user: ${yamlEsc(u.user)}\n`;
      y += `    pass: ${yamlEsc(u.pass)}\n`;
      y += '    ips: []\n';
      y += '    permissions:\n';
      y += '      - action: read\n';
      y += '        path: ""\n';
    }
    y += '\n';
  }
  y += 'pathDefaults:\n';
  y += '  record: yes\n';
  y += '  recordFormat: fmp4\n';
  y += `  recordPath: ${yamlEsc(PATHS.RECORD_ROOT)}/%path/%path_%Y-%m-%d_%H-%M-%S\n`;
  y += `  recordPartDuration: ${SETTINGS.RECORD_PART}\n`;
  y += `  recordSegmentDuration: ${SETTINGS.RECORD_SEGMENT}\n`;
  y += '  recordDeleteAfter: 0s\n';
  y += '  rtspTransport: tcp\n\n';
  y += 'paths:\n';
  for (const c of cameras) {
    y += `  ${c.name}:\n`;
    y += `    source: ${yamlEsc(c.rtsp)}\n`;
  }
  y += '\n';

  await ensureFileParentWritable(PATHS.MTX_FILE);
  try {
    await fsp.access(PATHS.MTX_FILE, fs.constants.F_OK);
    const backup = `${PATHS.MTX_FILE}.bak-${Date.now()}`;
    await fsp.copyFile(PATHS.MTX_FILE, backup);
    console.log(`[config] Backup creato: ${backup}`);
  } catch {}
  await fsp.writeFile(PATHS.MTX_FILE, y, 'utf8');
  console.log(`[config] Scritto ${PATHS.MTX_FILE}`);
}

// ============================ REGEN WRAPPER =========================
let regenLock = false;
async function regenerateFromCsv(reason = 'manual') {
  if (regenLock) {
    console.log(`[config] Rigenerazione già in corso, skip (${reason})`);
    return;
  }
  regenLock = true;
  try {
    const camRows = await parseCsv(PATHS.CAMERAS_CSV, 4); // NomeCam,IP,user,password
    const userRows = await parseCsv(PATHS.USERS_CSV, 2);  // user,password

    const cameras = camRows.map(([name, ip, user, pass]) => ({
      name, ip, user, pass, rtsp: urlWithCreds(ip, user, pass)
    }));
    if (cameras.length === 0) throw new Error('Nessuna telecamera in cameras.csv');

    const users = userRows.map(([user, pass]) => ({ user, pass }));
    if (users.length === 0) console.warn('[config] Nessun utente in users.csv: nessun accesso RTSP.');

    await generateMediamtxYaml(cameras, users);
    console.log(`[config] Rigenerazione completata (${reason}).`);
  } catch (e) {
    console.error(`[config] ERRORE rigenerazione (${reason}):`, e.message || e);
  } finally {
    regenLock = false;
  }
}

// ============================ JANITOR ===============================
let janitorRunning = false;
async function getFreeBytes(mountPath) {
  const s = await fsp.statfs(mountPath);
  return BigInt(s.bavail) * BigInt(s.bsize);
}
async function listCameraDirs() {
  const entries = await fsp.readdir(PATHS.RECORD_ROOT, { withFileTypes: true });
  return entries.filter(d => d.isDirectory()).map(d => d.name);
}
async function scanAllFilesOnce(camDirs) {
  const today = todayString();
  const perDate = new Map();
  for (const cam of camDirs) {
    const camDir = path.join(PATHS.RECORD_ROOT, cam);
    let ents; try { ents = await fsp.readdir(camDir, { withFileTypes: true }); }
    catch (e) { console.error(`[janitor] Errore dir ${camDir}: ${e.message}`); continue; }
    const camEsc = cam.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const re = new RegExp(`^${camEsc}_(\\d{4}-\\d{2}-\\d{2})_\\d{2}-\\d{2}-\\d{2}\\.[A-Za-z0-9]+$`);
    for (const de of ents) {
      if (!de.isFile()) continue;
      const m = de.name.match(re);
      if (!m) continue;
      const dateStr = m[1];
      if (dateStr === today) continue;
      const full = path.join(camDir, de.name);
      if (!perDate.has(dateStr)) perDate.set(dateStr, []);
      perDate.get(dateStr).push(full);
    }
  }
  const daysSorted = Array.from(perDate.keys()).sort();
  return { perDate, daysSorted };
}
async function deleteFilesBatch(filePaths) {
  const N = SETTINGS.UNLINK_CONCURRENCY;
  let deleted = 0, failed = 0;
  for (let i = 0; i < filePaths.length; i += N) {
    const chunk = filePaths.slice(i, i + N);
    await Promise.all(chunk.map(async p => {
      try { await fsp.unlink(p); deleted++; }
      catch (e) {
        if (e && (e.code === 'ENOENT' || e.code === 'ESTALE')) return;
        failed++; console.error('[janitor] Errore unlink', p, e.message);
      }
    }));
  }
  return { deleted, failed };
}
async function janitorCycle() {
  if (janitorRunning) return;
  janitorRunning = true;
  try {
    await ensureDirExists(PATHS.RECORD_ROOT);
    const free = await getFreeBytes(PATHS.DISK_CHECK);
    const freeGiB = Number(free / 1024n / 1024n / 1024n);
    console.log(`[janitor] Free: ${freeGiB} GiB (soglia 15 GiB)`);
    if (free >= SETTINGS.FREE_MIN_BYTES) return;

    const camDirs = await listCameraDirs();
    if (camDirs.length === 0) { console.warn('[janitor] Nessuna cartella telecamera.'); return; }

    const { perDate, daysSorted } = await scanAllFilesOnce(camDirs);
    if (daysSorted.length === 0) { console.warn('[janitor] Nessun giorno storico da eliminare.'); return; }

    const oldestDay = daysSorted[0];
    const targets = perDate.get(oldestDay) || [];
    if (targets.length === 0) { console.warn(`[janitor] Nessun file da eliminare per ${oldestDay}.`); return; }

    console.log(`[janitor] Sotto soglia: elimino ${oldestDay} (${targets.length} files).`);
    const t0 = Date.now();
    const { deleted, failed } = await deleteFilesBatch(targets);
    console.log(`[janitor] ${oldestDay}: eliminati ${deleted}, falliti ${failed}, tempo ${Date.now()-t0} ms.`);
  } catch (e) {
    console.error('[janitor] Errore generale:', e.message || e);
  } finally {
    janitorRunning = false;
  }
}

// ============================ WATCH CAMERAS.CSV =====================
let watchTimer = null;
let isWatcherActive = false;

function installCamerasWatcher() {
  // Evita watcher multipli
  if (isWatcherActive) {
    console.log('[config] Watcher già attivo, skip');
    return;
  }

  // fs.watchFile è più robusto ai salvataggi “atomic write” degli editor
  try {
    fs.watchFile(PATHS.CAMERAS_CSV, { persistent: true, interval: 1000 }, (curr, prev) => {
      if (curr.mtimeMs === prev.mtimeMs) return;
      if (watchTimer) clearTimeout(watchTimer);
      // debounce 500ms per evitare parse durante scrittura parziale
      watchTimer = setTimeout(() => regenerateFromCsv('watch:cameras.csv'), 500);
    });
    isWatcherActive = true;
    console.log('[config] Watch attivo su', PATHS.CAMERAS_CSV);
  } catch (e) {
    console.error('[config] Impossibile impostare il watcher:', e.message);
  }
}

function cleanupWatcher() {
  if (isWatcherActive) {
    try {
      fs.unwatchFile(PATHS.CAMERAS_CSV);
      if (watchTimer) {
        clearTimeout(watchTimer);
        watchTimer = null;
      }
      isWatcherActive = false;
      console.log('[config] Watcher rimosso');
    } catch (e) {
      console.error('[config] Errore rimozione watcher:', e.message);
    }
  }
}

// Gestione graceful shutdown
function setupGracefulShutdown() {
  const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
  signals.forEach(signal => {
    process.on(signal, () => {
      console.log(`[camserver] Ricevuto ${signal}, shutdown in corso...`);
      cleanupWatcher();
      process.exit(0);
    });
  });

  // Gestione errori non catturati
  process.on('uncaughtException', (err) => {
    console.error('[camserver] Errore non gestito:', err);
    cleanupWatcher();
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('[camserver] Promise rejection non gestita:', reason);
    cleanupWatcher();
    process.exit(1);
  });
}

// ============================ BOOTSTRAP =============================
(async () => {
  try {
    // Setup gestione graceful shutdown
    setupGracefulShutdown();

    await ensureDirExists(PATHS.MTX_DIR, true);
    await ensureFileParentWritable(PATHS.MTX_FILE);
    await ensureDirExists(PATHS.RECORD_ROOT, true);

    // Prima generazione
    await regenerateFromCsv('startup');

    // Avvia watcher su cameras.csv
    installCamerasWatcher();

    // Avvio janitor
    await janitorCycle();
    setInterval(janitorCycle, SETTINGS.CLEAN_INTERVAL_MS);

    console.log('[camserver] Avviato: config generata, watcher e janitor attivi.');
  } catch (e) {
    console.error('[camserver] ERRORE AVVIO:', e.message || e);
    cleanupWatcher(); // Cleanup anche in caso di errore di avvio
    process.exit(1);
  }
})();
